{"name": "my-app", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@langchain/anthropic": "^0.3.0", "@langchain/core": "^0.3.0", "@langchain/google-genai": "^0.1.0", "@langchain/openai": "^0.3.0", "axios": "^1.10.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^5.76.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "conf": "^14.0.0", "electron-store": "^8.2.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.23.12", "input-otp": "^1.4.2", "lucide-react": "^0.534.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-resizable-panels": "^3.0.1", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^1.1.2", "zod": "^3.24.4", "electron-updater": "^6.3.9", "execa": "^9.6.0", "find-process": "^2.0.0", "langchain": "^0.3.0", "lucide-vue-next": "^0.535.0", "node-cron": "^3.0.3", "radix-vue": "^1.9.17", "sqlite3": "^5.1.7", "tw-animate-css": "^1.3.6", "vue-router": "^4.4.0", "winston": "^3.17.0"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "electron": "^37.2.3", "electron-builder": "^25.1.8", "electron-vite": "^4.0.0", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "prettier": "^3.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^7.0.5", "@eslint/js": "^9.26.0", "autoprefixer": "^10.4.21", "globals": "^15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript-eslint": "^8.32.1", "@tailwindcss/typography": "^0.5.16", "@types/sqlite3": "^3.1.11", "@vitejs/plugin-vue": "^5.2.3", "@vitest/ui": "^2.0.0", "eslint-plugin-vue": "^10.0.0", "husky": "^9.0.0", "lint-staged": "^15.0.0", "shadcn-vue": "^2.2.0", "tailwindcss-animate": "^1.0.7", "vitest": "^2.0.0", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"]}}